import React from 'react';
import { DivinationCard } from './DivinationCard';
import { YaoLine, YaoType } from './YaoLine';
import { useTranslation } from '@/hooks/useTranslation';
import { translateSixSpirit, translateSixRelation, translateStemBranchElement } from '@/lib/utils';
import { Hexagram } from './HexagramUtils';

interface HexagramDisplayProps {
  hexagram: Hexagram;
}

export const HexagramDisplay: React.FC<HexagramDisplayProps> = ({ hexagram }) => {
  const { t } = useTranslation();
  
  return (
    <div className="grid md:grid-cols-2 gap-6">
      {/* 本卦 */}
      <DivinationCard title={t('originalHexagram')} className="md:col-span-1">
        <div className="space-y-4">
          {/* 卦象信息 */}
          <div className="text-center border-b pb-4">
            <h3 className="text-2xl font-bold gradient-text mb-2">
              {hexagram.name}
            </h3>
            {hexagram.hexagramType && (
              <div className="text-sm text-divination-accent mb-2">
                ({hexagram.hexagramType})
              </div>
            )}
          </div>

          {/* 爻象显示 */}
          <div className="space-y-1">
            <div className="grid grid-cols-[60px_120px_1fr_60px] gap-2 text-xs text-muted-foreground border-b pb-2">
              <div className="text-center">{t('sixSpirits')}</div>
              <div className="text-center">{t('sixRelations')}</div>
              <div className="text-center">{t('hexagramSymbol')}</div>
              <div className="text-center">{t('worldResponse')}</div>
            </div>
            
            {hexagram.lines.map((line, index) => {
              const position = 6 - index;
              const spiritIndex = (position - 1) % 6;
              const isWorld = position === hexagram.worldPosition;
              const isResponse = position === hexagram.responsePosition;

              // 查找伏在此爻位下的伏神
              const hiddenSpirit = hexagram.hiddenSpirits.find(hs => hs.hiddenUnder === position - 1);

              return (
                <div key={index} className="py-1">
                  <div className="grid grid-cols-[60px_120px_1fr_60px] gap-2 items-center">
                    <div className="text-xs text-divination-secondary text-center">
                      {translateSixSpirit(hexagram.sixSpirits[spiritIndex], t)}
                    </div>
                    <div className="text-xs text-center">
                      {translateSixRelation(hexagram.sixRelations[position - 1], t)}
                    </div>
                    <div className="flex justify-center">
                      <YaoLine type={line} position={position} className="mb-0" />
                    </div>
                    <div className="text-xs text-center">
                      {isWorld && <span className="text-divination-primary">{t('world')}</span>}
                      {isResponse && <span className="text-divination-accent">{t('response')}</span>}
                    </div>
                  </div>
                  {hiddenSpirit && (
                    <div className="grid grid-cols-[60px_120px_1fr_60px] gap-2 items-center mt-1">
                      <div className="text-xs text-muted-foreground"></div>
                      <div className="text-xs text-orange-600">
                        {t('hiddenSpirit')}：{translateSixRelation(hiddenSpirit.relation, t)}{translateStemBranchElement(hiddenSpirit.ganZhi.charAt(1) + hiddenSpirit.wuxing, t)}
                      </div>
                      <div></div>
                      <div></div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </DivinationCard>

      {/* 变卦 */}
      {hexagram.changedHexagram && (
        <DivinationCard title={t('changedHexagram')} className="md:col-span-1">
          <div className="space-y-4">
            <div className="text-center border-b pb-4">
              <h3 className="text-2xl font-bold gradient-text mb-2">
                {hexagram.changedHexagram.name}
              </h3>
              {/* 为了与本卦对齐，添加一个空的占位div */}
              <div className="text-sm text-divination-accent mb-2" style={{ visibility: 'hidden' }}>
                &nbsp;
              </div>
            </div>

            <div className="space-y-1">
              <div className="grid grid-cols-[60px_120px_1fr_60px] gap-2 text-xs text-muted-foreground border-b pb-2">
                <div className="text-center">{t('sixSpirits')}</div>
                <div className="text-center">{t('sixRelations')}</div>
                <div className="text-center">{t('hexagramSymbol')}</div>
                <div className="text-center">{t('worldResponse')}</div>
              </div>
              
              {hexagram.lines.map((line, index) => {
                const position = 6 - index;
                const spiritIndex = (position - 1) % 6;
                // 使用变卦自己的世应位置
                const isWorld = position === hexagram.changedHexagram?.worldPosition;
                const isResponse = position === hexagram.changedHexagram?.responsePosition;

                // 生成变卦爻线类型
                const changedLineType = line === 'yang_changing' ? 'yin' :
                                      line === 'yin_changing' ? 'yang' :
                                      line;

                return (
                  <div key={index} className="grid grid-cols-[60px_120px_1fr_60px] gap-2 items-center py-1">
                    <div className="text-xs text-divination-secondary text-center">
                      {translateSixSpirit(hexagram.sixSpirits[spiritIndex], t)}
                    </div>
                    <div className="text-xs text-center">
                      {hexagram.changedHexagram ? translateSixRelation(hexagram.changedHexagram.sixRelations[position - 1], t) : ''}
                    </div>
                    <div className="flex justify-center">
                      <YaoLine
                        type={changedLineType as YaoType}
                        position={position}
                        className="mb-0"
                      />
                    </div>
                    <div className="text-xs text-center">
                      {isWorld && <span className="text-divination-primary">{t('world')}</span>}
                      {isResponse && <span className="text-divination-accent">{t('response')}</span>}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </DivinationCard>
      )}
    </div>
  );
};